import { TJobSimulationEmail } from 'src/types';

const getEmails = (data: {
  jobSimulationId: string;
  logo: string;
  billionIntakeId?: string;
}): TJobSimulationEmail[] => [
  {
    id: '1',
    name: '<PERSON><PERSON> Greentek',
    avatar: 'https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-3.jpg',
    role: 'HR Manager',
    email: '<EMAIL>',
    title: 'Welcome to GreenTek Industries ESG Simulation!',
    desc: 'Welcome onboard! We are excited to have you join our ESG team.',
    nextEmailId: '2',
    data: {
      logo: data.logo || '/assets/greentek-logo.png',
      greeting: 'Hi there {user_name}',
      content: `I'm **HR Manager at GreenTek Industries**, and I'm thrilled you're joining us!

Our **job simulation** is crafted to give you a genuine experience of working in an ESG role.
You'll tackle real tasks, practical challenges, and clearly see how your efforts contribute to the bigger picture. Whether you're new to ESG or have some experience in sustainability, what counts most is your curiosity, critical thinking, and unique perspective.

To kick things off, we'd love to meet you in person (virtually!) and give you a warm introduction to the team, share more about what's ahead, and make sure you're all set for your journey.

I'll be setting up **a meeting** in just a few minutes so we can connect live—looking forward to seeing you there!
`,
      signature: {
        title: 'Regards',
        company: 'GreenTek Industries',
      },
    },
    triggerActions: [
      {
        type: 'nextEmail',
        data: { nextEmailId: '2', triggerTimeout: 3, when: 'open' },
      },
      {
        type: 'enableApps',
        data: {
          appIds: ['mail', 'news', 'meeting'],
          when: 'receive',
        },
      },
    ],
  },
  {
    id: '2',
    name: 'Julie Tan',
    role: 'Manager',
    avatar: '/assets/julie.jpeg',
    email: '<EMAIL>',
    title: 'Introductory Meeting Invitation',
    desc: 'Quickly understand your role and responsibilities in this simulation through a short onboarding meeting that walks you through key expectations and workflows.',
    nextEmailId: '3',
    data: {
      logo: data.logo || '/assets/greentek-logo.png',
      greeting: 'Hi {user_name}',
      content: `I'm **Julie Tan**, your manager here at Greentek Industries.
Today you'll be reviewing our ESG report and proposing some improvements.

To get us started on the right foot, I've scheduled a short virtual meeting so we can:
**Introduce ourselves and the team**
**Walk you through what to expect in the simulation**

I look forward to meeting you live and officially kicking things off together!
`,
      actions: [
        {
          type: 'joinMeeting',
          label: 'Join the Meeting',
          title: 'Onboarding Meeting',
          data: {
            datetime: '{email_time}',
            duration: '~1 minutes',
            meetingLink: 'https://dev-bitmeet.mnet.io/introduction/e94-12d-105?t=mi',
            completionMeetingActions: [
              {
                type: 'triggerAssistant',
                data: {
                  triggerMessage: 'I have completed the meeting with Julie.',
                },
              },
              {
                type: 'enableApps',
                data: {
                  appIds: ['mail', 'news', 'meeting', 'task-board', 'powerbi'],
                },
              },
              {
                type: 'sendEmailTask',
              },
            ],
          },
        },
      ],
      signature: {
        title: 'Warm regards',
        company: 'GreenTek Industries',
      },
    },
    triggerActions: [
      // {
      //   type: 'triggerAssistant',
      //   data: { triggerTimeout: 1, triggerMessage: "I've received the meeting invitation." },
      // },
      {
        type: 'triggerAssistant',
        data: {
          triggerTimeout: 1,
          triggerMessage: "I've received the meeting invitation.",
          when: 'receive',
        },
      },
    ],
  },
  {
    id: '3',
    name: 'Julie Tan',
    role: 'Manager',
    avatar: '/assets/julie.jpeg',
    email: '<EMAIL>',
    title: 'Thank You for Joining the ESG Simulation',
    desc: 'Congratulations on completing the simulation!',
    data: {
      logo: data.logo || '/assets/greentek-logo.png',
      greeting: 'Hi {user_name}',
      // TODO: write a content, congratulation user for joining the ESG simulation, complete all tasks, and show the reference letter. Thanks user for participating in the ESG simulation.
      content: `Congratulations! You've successfully completed the ESG Analyst simulation at Greentek.
You've demonstrated ESG insight, critical thinking, and great communication. Thank you for participating in the ESG simulation.

Here is your reference letter:
`,
      actions: [
        {
          type: 'viewFileDetail',
          label: 'View Reference Letter',
          title: 'Reference Letter',
          data: {
            fileUrl: `https://uat.internship.guru/en/public/reference-letter?programId=${data.billionIntakeId}&autoClaim=true`,
          },
        },
      ],
      signature: {
        title: 'regards',
        company: 'GreenTek Industries',
      },
    },
  },
];

// Build an email task, send from manager
const buildEmailTask = (prams: { jobSimulation: any; task: any }): TJobSimulationEmail => {
  const { jobSimulation, task } = prams;
  return {
    id: `email-task:${task.taskId}`,
    type: 'task',
    name: 'Julie Tan',
    avatar: '/assets/julie.jpeg',
    role: 'Manager',
    email: '<EMAIL>',
    title: `Task: ${task.taskName}`,
    desc: `New task assigned: ${task.taskName}`,
    data: {
      taskId: task.taskId,
      programId: jobSimulation.billionIntakeId,
      logo: jobSimulation.logo || '/assets/greentek-logo.png',
      greeting: 'Hi {user_name}',
      content: `You're doing great so far in the ESG Analyst Simulation at GreenTek Industries!
Your next task is titled: **${task.taskName}**

${task.taskDescription}

Please review the task carefully and complete it at your earliest convenience.
**Simply reply to this email with your completed task, or open the task board to work on the task.**
Good luck — we look forward to seeing your work!
`,
      actions: [],
      signature: {
        title: 'Best regards',
        company: jobSimulation.companyName,
      },
    },
    allowReply: true,
    triggerActions: [
      {
        type: 'triggerAssistant',
        data: {
          triggerTimeout: 1,
          triggerMessage: `I've received the task ${task.taskName}`,
          when: 'receive',
        },
      },
      {
        type: 'enableApps',
        data: {
          appIds: ['mail', 'news', 'meeting', 'task-board', 'powerbi'],
          when: 'receive',
        },
      },
    ],
  };
};

export default { buildEmailTask, getEmails };
