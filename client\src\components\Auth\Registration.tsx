import type { TError, TRegisterUser } from 'librechat-data-provider';
import { useRegisterUserMutation } from 'librechat-data-provider/react-query';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate, useOutletContext } from 'react-router-dom';
import type { TLoginLayoutContext } from '~/common';
import { Spinner } from '~/components/svg';
import { TranslationKeys, useLocalize } from '~/hooks';
import { ErrorMessage } from './ErrorMessage';

const MAX_COUNTDOWN = 2;

const Registration: React.FC = () => {
  const navigate = useNavigate();
  const localize = useLocalize();
  const { startupConfig, startupConfigError, isFetching, jobSimulationId } =
    useOutletContext<TLoginLayoutContext>();

  const {
    watch,
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<TRegisterUser>({ mode: 'onChange' });
  const password = watch('password');

  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [countdown, setCountdown] = useState<number>(MAX_COUNTDOWN);
  const [isEmployer, setIsEmployer] = useState(false);

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get('token');

  const registerUser = useRegisterUserMutation({
    onMutate: () => {
      setIsSubmitting(true);
    },
    onSuccess: () => {
      setIsSubmitting(false);
      setCountdown(MAX_COUNTDOWN);
      const timer = setInterval(() => {
        setCountdown((prevCountdown) => {
          if (prevCountdown <= 1) {
            clearInterval(timer);
            // const redirectPath = jobSimulationId ? `/job-simulation/${jobSimulationId}` : '/c/new';
            const redirectPath = isEmployer
              ? '/job-simulation/dashboard'
              : `/job-simulation/${jobSimulationId ?? 'list'}`;
            navigate(redirectPath, { replace: true });
            return 0;
          } else {
            return prevCountdown - 1;
          }
        });
      }, 1000);
    },
    onError: (error: unknown) => {
      setIsSubmitting(false);
      if ((error as TError).response?.data?.message) {
        setErrorMessage((error as TError).response?.data?.message ?? '');
      }
    },
  });

  const renderInput = (id: string, label: TranslationKeys, type: string, validation: object) => (
    <div className="mb-4">
      <div className="relative">
        <input
          id={id}
          type={type}
          autoComplete={id}
          aria-label={localize(label)}
          {...register(
            id as 'name' | 'email' | 'username' | 'password' | 'confirm_password',
            validation,
          )}
          aria-invalid={!!errors[id]}
          className="webkit-dark-styles transition-color peer w-full rounded-2xl border border-border-light bg-surface-primary px-3.5 pb-2.5 pt-3 text-text-primary duration-200 focus:border-yellow-500 focus:outline-none"
          placeholder=" "
          data-testid={id}
        />
        <label
          htmlFor={id}
          className="absolute start-3 top-1.5 z-10 origin-[0] -translate-y-4 scale-75 transform bg-surface-primary px-2 text-sm text-text-secondary-alt duration-200 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-1.5 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-yellow-600 dark:peer-focus:text-yellow-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4"
        >
          {localize(label)}
        </label>
      </div>
      {errors[id] && (
        <span role="alert" className="mt-1 text-sm text-red-500">
          {String(errors[id]?.message) ?? ''}
        </span>
      )}
    </div>
  );

  return (
    <>
      {errorMessage && (
        <ErrorMessage>
          {localize('com_auth_error_create')}
          <br />
          <br />
          <span className="italic">{errorMessage}</span>
        </ErrorMessage>
      )}
      {registerUser.isSuccess && countdown > 0 && (
        <div
          className="rounded-md border border-green-500 bg-green-500/10 px-3 py-2 text-sm text-gray-600 dark:text-gray-200"
          role="alert"
        >
          {localize(
            startupConfig?.emailEnabled
              ? 'com_auth_registration_success_generic'
              : 'com_auth_registration_success_insecure',
          ) +
            ' ' +
            localize('com_auth_email_verification_redirecting', { 0: countdown.toString() })}
        </div>
      )}
      <>
        <form
          className="mt-6 w-full"
          aria-label="Registration form"
          method="POST"
          onSubmit={handleSubmit((data: TRegisterUser) =>
            registerUser.mutate({ ...data, isEmployer: isEmployer, token: token ?? undefined }),
          )}
        >
          {renderInput('name', 'com_auth_full_name', 'text', {
            required: localize('com_auth_name_required'),
            minLength: {
              value: 3,
              message: localize('com_auth_name_min_length'),
            },
            maxLength: {
              value: 80,
              message: localize('com_auth_name_max_length'),
            },
          })}
          {renderInput('email', 'com_auth_email', 'email', {
            required: localize('com_auth_email_required'),
            minLength: {
              value: 1,
              message: localize('com_auth_email_min_length'),
            },
            maxLength: {
              value: 120,
              message: localize('com_auth_email_max_length'),
            },
            pattern: {
              value: /\S+@\S+\.\S+/,
              message: localize('com_auth_email_pattern'),
            },
          })}
          {renderInput('password', 'com_auth_password', 'password', {
            required: localize('com_auth_password_required'),
            minLength: {
              value: 8,
              message: localize('com_auth_password_min_length'),
            },
            maxLength: {
              value: 128,
              message: localize('com_auth_password_max_length'),
            },
          })}
          {renderInput('confirm_password', 'com_auth_password_confirm', 'password', {
            validate: (value: string) =>
              value === password || localize('com_auth_password_not_match'),
          })}
          <div className="mb-4 flex items-center gap-2">
            <input
              type="checkbox"
              id="isEmployer"
              checked={isEmployer}
              onChange={(e) => setIsEmployer(e.target.checked)}
              className="h-4 w-4 text-job-primary bg-job-primary border-gray-300 rounded"
            />
            <label htmlFor="isEmployer" className="text-sm text-gray-700 dark:text-white">
              I'm a Employer
            </label>
          </div>
          <div className="mt-6">
            <button
              disabled={Object.keys(errors).length > 0 || isSubmitting}
              type="submit"
              aria-label="Submit registration"
              // className="disabled:hover:gradient-button gradient-button flex w-full items-center justify-center gap-2 rounded-2xl px-4 py-3 text-sm font-medium text-white transition-colors focus:outline-none focus:ring-offset-2 disabled:opacity-50"
              className="disabled:hover:gradient-button flex w-full items-center justify-center gap-2 rounded-full bg-job-primary px-4 py-3 transition-colors focus:outline-none focus:ring-offset-2 disabled:opacity-50"
            >
              {/* <img src="/assets/start-left.png" alt="Icon Left Button" /> */}
              {isSubmitting ? <Spinner size="1.5em" /> : 'Sign up'}
              {/* <img src="/assets/start-right.png" alt="Icon Right Button" /> */}
            </button>
          </div>
        </form>

        <p className="my-4 text-center font-light text-gray-700 dark:text-white">
          Already have an account?
          <a
            href={jobSimulationId ? `/login?jobSimulationId=${jobSimulationId}` : '/login'}
            aria-label="Login"
            className="inline-flex p-1 font-medium text-job-primary underline transition-colors"
          >
            Sign in now
          </a>
        </p>
      </>
    </>
  );
};

export default Registration;
